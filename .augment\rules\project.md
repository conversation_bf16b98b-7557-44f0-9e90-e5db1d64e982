---
type: "manual"
---

以下是为你制定的项目相关的规则。

---

### **React Native 原生 CLI 项目初始化与框架选型规则**

#### **A. 项目初始化与环境配置**

1.  **初始化命令**
    *   `npx react-native@latest init YourProjectName`

2.  **启用新架构 (强制)**
    *   **Android**: 修改 `android/gradle.properties`，设置 `newArchEnabled=true`。
    *   **iOS**: 在 `ios` 目录下，执行 `RCT_NEW_ARCH_ENABLED=1 bundle exec pod install`。

3.  **语言**
    *   **强制使用 TypeScript**。项目初始化时选择 TypeScript 模板，或手动迁移。

4.  **版本控制**
    *   初始化后立即配置 `.gitignore`，并提交第一次 commit。

#### **B. 核心框架与库选型规则**

| 类别 | 选用规则 | 备选/特定场景 |
| :--- | :--- | :--- |
| **导航** | **`react-native-navigation`** (Wix) | `@react-navigation/native-stack` |
| **状态管理 (客户端)** | **`Zustand`** | `Jotai` |
| **状态管理 (服务端)** | **`TanStack Query` (React Query)** | - |
| **样式** | **`StyleSheet.create`** (内置) | - |
| **列表** | **`FlashList`** (Shopify) | - |
| **动画 & 手势**| **`React Native Reanimated`** | - |
| **高性能图形** | **`React Native Skia`** (Shopify) | - |
| **数据存储 (键值对)**| **`React Native MMKV`** | - |
| **数据存储 (数据库)**| **`WatermelonDB`** | `Realm` |
| **环境变量** | **`react-native-config`** | - |
| **图标** | **`react-native-vector-icons`** | 使用自定义字体图标 |

#### **C. 原生依赖与模块管理规则**

1.  **iOS 依赖管理**
    *   **唯一工具**: CocoaPods。
    *   **操作文件**: `ios/Podfile`。
    *   **安装命令**: `cd ios && pod install`。

2.  **Android 依赖管理**
    *   **唯一工具**: Gradle。
    *   **操作文件**: `android/app/build.gradle` 和 `android/build.gradle`。

3.  **原生模块桥接**
    *   **新模块**: 必须使用 **TurboModules** 规范编写。
    *   **新UI组件**: 必须使用 **Fabric Native Components** 规范编写。

#### **D. 开发与调试工作流规则**

1.  **IDE 使用**
    *   **JavaScript/TypeScript**: VS Code。
    *   **iOS 原生层**: Xcode (必须使用 `.xcworkspace` 文件)。
    *   **Android 原生层**: Android Studio。

2.  **代码质量**
    *   **格式化**: Prettier。
    *   **Linter**: ESLint。
    *   **强制执行**: 使用 `husky` 和 `lint-staged` 在 `git commit` 时自动检查。

3.  **启动与运行**
    *   **标准命令**: `npx react-native run-ios` 和 `npx react-native run-android`。
    *   **推荐**: 直接通过 Xcode 或 Android Studio 的 "Run" 按钮启动，以便查看原生日志和调试信息。

4.  **调试**
    *   **JS 调试**: Flipper (默认) 或 Chrome DevTools。
    *   **原生调试 (iOS)**: Xcode 的内置调试器和 Instruments。
    *   **原生调试 (Android)**: Android Studio 的内置调试器和 Profiler。

#### **E. 构建与发布规则**

1.  **应用图标与启动屏**
    *   **工具**: `react-native-bootsplash`。
    *   **规则**: 必须为 iOS 和 Android 分别配置原生启动屏，禁止使用纯 JS 实现的启动屏。

2.  **版本管理**
    *   **工具**: `react-native-version` 或手动修改。
    *   **规则**: 严格遵循 `major.minor.patch` 语义化版本号，并对应修改 `package.json`, `build.gradle`, `Info.plist`。

3.  **iOS 构建**
    *   **方式**: 通过 Xcode 的 `Archive` 功能生成 `.ipa` 文件。
    *   **证书管理**: 必须使用 Xcode 的自动或手动签名机制。

4.  **Android 构建**
    *   **方式**: 在 `android` 目录下执行 `./gradlew assembleRelease` 或 `./gradlew bundleRelease`。
    *   **签名管理**: 必须在 `android/app/build.gradle` 中配置生产环境的 `signingConfigs`，并将签名文件 (`.keystore`) 妥善保管。

---
*该文档最后更新于 2024年5月。*